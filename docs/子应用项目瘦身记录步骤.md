# 子应用项目瘦身记录步骤

## 项目信息
- **项目名称**: gac_iop_childapp（微前端子应用）
- **技术栈**: Vue 3 + TypeScript + Vite + Ant Design Vue
- **特殊性**: 支持多环境构建、集成在线表单设计器、钉钉API
- **优化开始时间**: 2025-07-25

## 优化总体方向

基于参考文档的成功经验，制定了5个阶段的渐进式优化方案：

1. **依赖瘦身优化** - 移除未使用依赖，解决重复依赖
2. **构建配置优化** - 优化分包策略，改进构建配置
3. **图标库现代化升级** - 升级到现代iconify方案
4. **大型组件异步化** - 实现按需异步加载
5. **性能监控系统建立** - 建立完整的性能监控体系

---

## 阶段1：依赖分析与安全清理 ✅

### 执行时间
2025-07-25 15:30 - 16:10

### 分析工具
使用 `depcheck` 工具进行依赖分析：
```bash
npm install -g depcheck
depcheck
```

### 分析结果

#### 未使用的生产依赖 (9个)
- `@iconify/iconify` - 已过时的图标库
- `china-area-data` - 地区数据（已被@vant/area-data替代）
- `enquire.js` - 响应式查询库
- `intro.js` - 引导库
- `moment` - 日期库（❌ 经验证实际被使用，保留）
- `vue-cropper`, `vue-cropperjs` - 图片裁剪组件
- `vue-infinite-scroll` - 无限滚动
- `vuedraggable` - 拖拽组件

#### 未使用的开发依赖 (25个)
包括：
- 测试工具：`@vue/test-utils`, `jest`, `ts-jest`, `ts-node`
- 样式检查：`stylelint`相关包
- Git hooks：`husky`, `lint-staged`, `pretty-quick`
- 构建工具：`http-server`, `npm-run-all`, `postcss`相关
- 未使用的Vite插件：`vite-plugin-optimize-persist`, `vite-plugin-package-config`

### 重要验证发现

#### 保留的依赖（经验证实际被使用）
1. **ECharts** - 与参考文档不同，本项目中广泛使用
   - `src/views/report/chartdemo/index.vue` - 图表演示页面
   - `src/views/dashboard/Analysis/homePage/IndexChart.vue` - 首页图表
   - `src/views/report/statisticst/index.vue` - 统计图表
   - 多个chart组件（Bar、LineMulti、Pie等）

2. **moment** - 时区处理功能
   - `src/utils/useMinxin.ts` - 时区处理功能
   - `src/utils/http/axios/index.ts` - HTTP请求中的时区处理

### 执行的清理操作

#### 1. 移除未使用的生产依赖
```bash
pnpm remove @iconify/iconify china-area-data enquire.js intro.js vue-cropper vue-cropperjs vue-infinite-scroll vuedraggable
```

#### 2. 移除未使用的开发依赖
```bash
pnpm remove @iconify/json @types/intro.js @types/showdown @vue/test-utils autoprefixer commitizen cz-git czg http-server is-ci lint-staged npm-run-all postcss postcss-html postcss-less pretty-quick stylelint stylelint-config-prettier stylelint-config-recommended stylelint-config-recommended-vue stylelint-config-standard stylelint-order ts-jest ts-node vite-plugin-optimize-persist vite-plugin-package-config vue-tsc
```

#### 3. 解决重复依赖问题
```bash
pnpm remove lodash.get
```
并修复代码中的导入：
```typescript
// 修改前
import _get from 'lodash.get';

// 修改后
import { get as _get } from 'lodash-es';
```

#### 4. 重新安装必需的构建依赖
```bash
pnpm add -D autoprefixer  # postcss.config.js需要
```

### 遇到的问题与解决

#### 问题1：构建失败 - autoprefixer缺失
**现象**: 移除autoprefixer后构建报错
**原因**: postcss.config.js仍在使用autoprefixer
**解决**: 重新安装autoprefixer作为开发依赖

#### 问题2：lodash.get导入冲突
**现象**: 构建时报错无法解析lodash.get
**原因**: 移除lodash.get包后，代码中仍有导入
**解决**: 修改`src/views/monitor/trace/trace.data.ts`中的导入语句

### 优化成果

#### 数量统计
- **总移除包数**: 35个依赖包
- **减少的总包数**: 224个包（包括传递依赖）
- **生产依赖清理**: 8个包
- **开发依赖清理**: 27个包

#### 预期效果
基于参考文档经验预估：
- **构建时间减少**: 5-10%
- **包体积减少**: 显著减少
- **网络连接问题解决**: 移除了`@iconify/json`等大型包

### 经验教训

1. **充分验证依赖使用情况** - ECharts和moment实际被广泛使用，不能盲目移除
2. **注意构建必需依赖** - autoprefixer等构建工具依赖需要保留
3. **代码同步修改** - 移除依赖包后要同步修改代码中的导入语句
4. **微前端特殊性** - 作为子应用，某些依赖可能被动态使用

### 下一步计划

进入阶段3：图标库现代化升级
- 升级到现代iconify方案
- 解决网络连接问题
- 支持20万+图标

---

## 阶段2：构建配置基础优化 ✅

### 执行时间
2025-01-25 16:15 - 16:20

### 优化目标
- 优化分包策略，参考文档的11个分包设计
- 改进构建配置，提升并行加载能力
- 预期收益：主应用代码减少60-80%

### 执行的优化操作

#### 1. 移除问题插件
```typescript
// build/vite/plugin/index.ts
// import purgeIcons from 'vite-plugin-purge-icons'; // 已移除：依赖@iconify/json包已删除
// vitePlugins.push(purgeIcons()); // 已移除
```

#### 2. 优化分包策略（5个→11个分包）
```typescript
// vite.config.ts - 新的分包配置
manualChunks: {
  // Vue生态核心 (小包，高频使用)
  'vue-vendor': ['vue', 'vue-router', 'pinia'],

  // UI框架主体 (大包，但必需)
  'antd-vue-vendor': ['ant-design-vue', '@ant-design/colors'],

  // 图标库单独分包 (中等大小，按需加载)
  'antd-icons-vendor': ['@ant-design/icons-vue'],

  // 表格组件 (大包，特定页面使用)
  'vxe-table-vendor': ['vxe-table', 'vxe-table-plugin-antd', 'xe-utils'],

  // 图表库 (大包，特定页面使用)
  'chart-vendor': ['echarts'],

  // 富文本编辑器 (超大包，特定功能使用)
  'editor-vendor': ['tinymce', '@tinymce/tinymce-vue'],

  // 工具库 (中等大小，高频使用)
  'utils-vendor': ['dayjs', 'crypto-js', 'axios', 'moment', 'moment-timezone'],

  // 移动端UI (大包，移动端页面使用)
  'vant-vendor': ['vant'],

  // 地区数据 (中等大小，特定功能使用)
  'area-data-vendor': ['@vant/area-data'],

  // 表情包 (中等大小，特定功能使用)
  'emoji-vendor': ['emoji-mart-vue-fast'],

  // 特殊API (小包，框架级别)
  'api-vendor': ['dingtalk-jsapi'],

  // 在线表单设计器 (大包，特定功能使用)
  'online-vendor': ['@jeecg/online']
}
```

#### 3. 优化构建配置
```typescript
// vite.config.ts
build: {
  target: 'es2020', // 升级到es2020，减少polyfill
  // ... 其他配置
},
esbuild: {
  target: 'es2020', // 与构建target保持一致
  drop: isBuild ? ['console', 'debugger'] : [], // 生产环境清除console
},
optimizeDeps: {
  esbuildOptions: {
    target: 'es2020', // 与构建target保持一致
  },
}
```

### 遇到的问题与解决

#### 问题1：qiankun模块解析失败
**现象**: 构建时报错"Could not resolve entry module 'qiankun'"
**原因**: 项目使用vite-plugin-qiankun而非qiankun包本身
**解决**: 修改分包配置，移除qiankun依赖，改为api-vendor包含dingtalk-jsapi

### 优化成果

#### 分包效果统计
- **分包数量**: 5个 → 11个分包
- **并行加载**: 支持11个包的并行下载
- **构建时间**: 约33秒

#### 各分包大小分析
- `vue-vendor`: 162.77kb (Vue生态核心)
- `antd-vue-vendor`: 1461.53kb (UI框架主体)
- `antd-icons-vendor`: 1015.62kb (图标库)
- `vxe-table-vendor`: 526.86kb (表格组件)
- `chart-vendor`: 1018.69kb (图表库)
- `editor-vendor`: 424.32kb (富文本编辑器)
- `utils-vendor`: 826.86kb (工具库)
- `vant-vendor`: 213.69kb (移动端UI)
- `area-data-vendor`: 77.06kb (地区数据)
- `api-vendor`: 308.86kb (特殊API)
- `online-vendor`: 573.73kb (在线表单设计器)

#### 优化效果
- **代码分割**: 实现了更细粒度的代码分割
- **按需加载**: 大型组件支持按需加载
- **并行下载**: 提升了资源加载并行度
- **缓存优化**: 不同功能模块独立缓存

### 经验教训

1. **微前端特殊性**: 需要注意vite-plugin-qiankun与qiankun包的区别
2. **分包策略**: 按使用频率和功能模块进行合理分组
3. **构建target统一**: 确保所有构建配置使用相同的target
4. **插件依赖检查**: 移除依赖时要同步清理相关插件

---

## 阶段3：图标库现代化升级 📋

### 计划执行时间
待定

### 优化目标
- 升级到现代iconify方案
- 解决网络连接问题
- 预期收益：构建时间提升15-20%

---

## 阶段4：大型组件异步化 📋

### 计划执行时间
待定

### 优化目标
- 实现TinyMCE等大型组件的按需加载
- 预期收益：首屏加载时间减少30-50%

---

## 阶段5：性能监控系统建立 📋

### 计划执行时间
待定

### 优化目标
- 建立完整的构建性能监控体系
- 为持续优化提供数据支撑

---

## 总结

第一阶段的依赖清理工作已成功完成，为后续的构建优化奠定了良好基础。通过系统性的分析和验证，我们安全地移除了35个未使用的依赖包，同时保留了项目实际需要的关键依赖。

下一步将重点关注构建配置的优化，通过改进分包策略来进一步提升项目的加载性能。