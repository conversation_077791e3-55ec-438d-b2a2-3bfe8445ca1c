import { defHttp } from '/@/utils/http/axios'

/**
 * 查询销售经理线索工作台数据
 * @param params
 */
export const apiClueWorkbenchInfo = params => {
    return defHttp.post({ url: '/manage/clue/workbench/queryClueWorkbenchInfo', params })
}

/**
 * 查询线索专员线索工作台信息
 * @param params
 */
export const apiSpecialistClueWorkbenchInfo = params => {
    return defHttp.post({ url: '/manage/clue/workbench/querySpecialistClueWorkbenchInfo', params })
}

/**
 * 有效线索 线索分配 分页查询
 * @param params
 */
export const apiPageDisClueDLR = params => {
    return defHttp.post({ url: '/manage/clue/clueValid/pageDisClueDLR', params })
}

/**
 * // 领取有效线索
 * @param params
 */
export const apiReceiveClueValid = params => {
    return defHttp.post({ url: '/manage/clue/clueValid/receiveClueValid', params })
}

/**
 * 详情
 * @param params
 */
export const apiGetDetails = params => defHttp.get({ url: '/manage/clue/clueValid/detail', params })

/**
 * 跟进记录
 * @param params
 */
export const apiClueValidFollow = params => defHttp.get({ url: '/manage/clue/clueValidFollow/detailAll', params })

/**
 * 新增跟进记录
 * @param params
 */
export const apiClueValidFollowAdd = params => defHttp.post({ url: '/manage/clue/clueValidFollow/add', params })

/**
 * 新增到店记录
 * @param params
 */
export const apiValidReachStoreAdd = params => defHttp.post({ url: '/manage/clue/clueValidReachStore/add', params })

/**
 * 获取所有品牌+车系+车型
 * flag：true可以查国家+城市
 */
export const apiQueryAllBrand = () => {
    const params = { level: 3 }
    return defHttp.post({ url: '/manage/base/manage/iop/car/brand/queryAll/groupBy/modelName', params })
}

/**
 * 新增试驾记录
 * @param params
 */
export const apiClueValidDriveAdd = params => defHttp.post({ url: '/manage/clue/clueValidDrive/add', params })

/**
 * 新增下定记录
 * @param params
 */
export const apiClueValidPlaceOrderAdd = params => defHttp.post({ url: '/manage/clue/clueValidPlaceOrder/add', params })

/**
 * 新增战败记录
 * @param params
 */
export const apiClueValidLoseAdd = params => defHttp.post({ url: '/manage/clue/clueValidLose/add', params })

/**
 * H5 查询线索专员线索 分配人员
 * @param params
 */
export const apiSpecialistClueWorkbenchInfoH5 = params => defHttp.post({ url: ' /manage/clue/workbench/querySpecialistClueWorkbenchInfoH5', params })

/**
 * H5 批量分配销售顾问
 * @param params
 */
export const apiSalesConsultant = params => defHttp.post({ url: '/manage/clue/clueValid/disSalesConsultant', params })

/**
 * 根根据有效线索id 查询全部到店记录
 * @param params
 */
export const apiClueValidReachStore = params => defHttp.get({ url: '/manage/clue/clueValidReachStore/detailAll', params })

/**
 * 根根据有效线索id 查询试驾信息
 * @param params
 */
export const apiClueValidDrive = params => defHttp.get({ url: '/manage/clue/clueValidDrive/detailAll', params })

/**
 * 根根据有效线索id 查询下定信息
 * @param params
 */
export const apiClueValidPlaceOrder = params => defHttp.get({ url: '/manage/clue/clueValidPlaceOrder/detailAll', params })

/**
 * 根根据有效线索id 查询战败信息
 * @param params
 */
export const apiClueValidLose = params => defHttp.get({ url: '/manage/clue/clueValidLose/detailAll', params })

/**
 * 根据有效线索id 查询成交记录 详情
 * @param params
 */
export const apiClueValidOrder = params => defHttp.get({ url: '/manage/clue/clueValidOrder/detailAll', params })

/**
 * 经理查询所有员工的工单情况
 * @data 待分配：allocatedOrderCount
 * @data 驳回待分配：rejectedOrderCount
 * @data 待处理：dealingOrderCount

 * @param params
 */
export const apiGetStaffOrderInfo = params => defHttp.post({ url: '/manage/workorder/workbench/getStaffOrderInfo', params })

/**
 * 当前员工查询工单基本信息
 * @param params
 */
export const getOrderInfoByUser = params => defHttp.post({ url: '/manage/workorder/workbench/getOrderInfoByUser', params })

/**
 * 工单列表
 * @param params
 */
export const apiListOrder = params => defHttp.post({ url: '/manage/workorder/order/listOrder', params })

/**
 * 查询全部专员
 * @param params
 */
export const getStaffListApi = params => defHttp.post({ url: '/manage/workorder/workbench/getStaffList', params })

/**
 * 获取网点
 * @data userid 用户id
 */
export const querListByUserNetworkApi = data => {
    return defHttp.post({ url: `/manage/base/manage/iop/user/network/querListByUserId/${data.userId}` })
}

/**
 * 工单处理列表
 * @param params
 */
export const listOrderHandle = params => defHttp.post({ url: '/manage/workorder/order/listOrderHandle', params })

/**
 * 工单分配-销售员列表
 * @param params
 */
export const apiListStaffInfo = params => defHttp.post({ url: '/manage/workorder/order/listStaffInfo', params })

/**
 * 工单分配
 * @param params
 */
export const apiInsertOrderDealStaff = params => defHttp.post({ url: '/manage/workorder/order/insertOrderDealStaff', params })

/**
 *  查询经销商信息接口
 * @param params
 */
export const apiListDealerInfoList = params => defHttp.post({ url: '/manage/workorder/order/listDealerInfoList', params })

/**
 *  查询经销商信息接口
 * @param params
 */
export const queryNextLevel = params => defHttp.post({ url: '/manage/base/manage/iop/dealer/network/queryNextLevel', params })

/**
 *  工单由厂端转派到经销商
 * @param params
 */
export const apiTransferToDealer = params => defHttp.post({ url: '/manage/workorder/order/transferToDealer', params })

/**
 *  工单处理
 * @param params
 */
export const apiInsertOrderOperateRecord = params => defHttp.post({ url: '/manage/workorder/order/insertOrderOperateRecord', params })

/**
 *  工单基础信息
 * @param params
 */
export const apiSelectOne = params => defHttp.get({ url: '/manage/workorder/order/selectOne', params })

/**
 *  根据订单号查询车辆(脱敏)
 * @param params
 */
export const apiSelectCar = params => defHttp.get({ url: '/manage/workorder/order/selectCar', params })

/**
 *  工单履历信息
 * @param params
 */
export const apiSelectRecordList = params => defHttp.get({ url: '/manage/workorder/order-operate-record/selectRecordList', params })

/**
 *  工单客户信息信息
 * @param params
 */
export const getCustomerDetails = params => defHttp.get({ url: '/manage/workorder/order/selectCustomer', params })

/**
 * @description: 切换门店
 */
export const switchStore = data => {
    return defHttp.post<{
        networkCode: string
    }>({ url: '/manage/base/manage/iop/sys/store/user/switchStore', data })
}
