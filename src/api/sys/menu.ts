import { defHttp } from '/@/utils/http/axios';
export interface GetAuthRes {
  createTime: string;
  icon: string;
  menuId: string;
  name: string;
  orderNum: number;
  parentId: string;
  perms: string;
  type: number; // 0：目录 1：菜单 2：字段
  updateTime: string;
  url?: string;
}
// 后端返回的菜单数据类型
export interface GetMenuRes {
  attributes?: {
    icon: string;
    url: string;
  };
  checked: string;
  children: GetMenuRes[] | undefined;
  // 是否存在下级
  hasChildren: boolean;
  // 是否存在上级
  hasParent: boolean;
  id: string;
  parentId: string;
  state?: { opened: boolean };
  text: string;
}
// 后端返回的权限数据类型
export interface GetRoleAndMenuRes {
  // 所有权限编码
  allAuth: string[];
  // 所有纯权限编码
  codeList: string[];
  // 当前用户所拥有的权限-包含目录、菜单和字段
  auth: GetAuthRes[];
  // 当前框架菜单树
  menu: GetMenuRes[];
  // 所有菜单集合
  allTypeMenuList: GetAuthRes[];
  // 按钮类型集
  buttonMenuList: GetAuthRes[];
  // 目录类型集合
  catalogMenuList: GetAuthRes[];
  // 菜单类型集合
  menuList: GetAuthRes[];
  // 系统安全模式
  sysSafeMode: boolean;
  // 角色集合
  userRoleList: {
    id: number;
    roleId: number;
    userId: number;
    [key: string]: any;
  }[];
}

enum Api {
  GetMenuList = '/manage/base/manage/iop/sys/role/queryRoleAndMenuByUserId',
  // 【QQYUN-8487】
  // SwitchVue3Menu = '/sys/switchVue3Menu',
}

/**
 * @description: Get user menu based on id
 */

export const getMenuList = () => {
  return new Promise((resolve) => {
    defHttp.post<GetRoleAndMenuRes>({ url: Api.GetMenuList }).then((res) => {
      resolve(res?.menu || []);
    });
  });
};

/**
 * @description: 获取后台菜单权限和按钮权限
 */
export const getBackMenuAndPerms = (data: { userId: string }) => {
  return new Promise((resolve) => {
    defHttp.post<GetRoleAndMenuRes>({ url: Api.GetMenuList, data }).then((res) => {
      resolve(res);
    });
  });
};

/**
 * 切换成vue3菜单
 */
// update-begin--author:liaozhiyang---date:20240313---for：【QQYUN-8487】注释掉判断菜单是否vue2版本逻辑代码
// export const switchVue3Menu = () => {
//   return new Promise((resolve) => {
//     defHttp.get({ url: Api.SwitchVue3Menu });
//   });
// };
// update-end--author:liaozhiyang---date:20240313---for：【QQYUN-8487】注释掉判断菜单是否vue2版本逻辑代码
