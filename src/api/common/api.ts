import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
const globSetting = useGlobSetting();
const baseUploadUrl = globSetting.uploadUrl;
enum Api {
  positionList = '/sys/position/list',
  userList = '/sys/user/list',
  roleList = '/sys/role/list',
  queryDepartTreeSync = '/sys/sysDepart/queryDepartTreeSync',
  queryTreeList = '/sys/sysDepart/queryTreeList',
  loadTreeData = '/sys/category/loadTreeData',
  loadDictItem = '/sys/category/loadDictItem/',
  getDictItems = '/sys/dict/getDictItems/',
  getTableList = '/sys/user/queryUserComponentData',
  getCategoryData = '/sys/category/loadAllData',
  duplicateCheck = '/sys/duplicate/check',
  refreshCache = '/sys/dict/refreshCache',
  queryAllDictItems = '/sys/dict/queryAllDictItems',
  getUserDeparts = '/sys/user/getUserDeparts',
  selectDepart = '/sys/user/selectDepart',
  getUserTenants = '/sys/user/getUserTenants',
}

/**
 * 发送登录验证码
 */
export const sendLoginMsgApi = (params: {
  account: string; // 账号名
}) => {
  return defHttp.post({ url: '/sms/login/sendMsg', params });
};

/**
 * 获取支持语言接口
 */
export const getLanguageTypeApi = (params) => {
  return defHttp.post({ url: '/manage/base/manage/iop/language/v1/getLanguageType', params });
};

/**
 * 获取支持语言接口
 */
export const i18nGetAll = (params) => {
  return defHttp.post({ url: '/manage/common/i18n/getAll', params });
};

/**
 * 操作日志接口
 */
export const getLogInfoApi = (params: {
  bizId: string; // 数据业务id ,
  bizType: number; // 业务类型 1:角色管理 ,
  pageIndex: number; //  当前页码 ,
  pageSize: number; // 每页查询数量
}) => {
  return defHttp.post({ url: '/action/log/logInfo', params });
};

/**
 * 上传父路径
 */
export const uploadUrl = `/manage/common/file-info/upload-file`;

/**
 * 职务列表
 * @param params
 */
export const getPositionList = (params) => {
  return defHttp.get({ url: Api.positionList, params });
};

/**
 * 用户列表
 * @param params
 */
export const getUserList = (params) => {
  return defHttp.get({ url: Api.userList, params });
};

/**
 * 角色列表
 * @param params
 */
export const getRoleList = (params) => {
  return defHttp.get({ url: Api.roleList, params });
};

/**
 * 异步获取部门树列表
 */
export const queryDepartTreeSync = (params?) => {
  return defHttp.get({ url: Api.queryDepartTreeSync, params });
};
/**
 * 获取部门树列表
 */
export const queryTreeList = (params?) => {
  return defHttp.get({ url: Api.queryTreeList, params });
};

/**
 * 分类字典树控件 加载节点
 */
export const loadTreeData = (params?) => {
  return defHttp.get({ url: Api.loadTreeData, params });
};

/**
 * 根据字典code加载字典text
 */
export const loadDictItem = (params?) => {
  return defHttp.get({ url: Api.loadDictItem, params });
};

/**
 * 根据字典code加载字典text
 */
export const getDictItems = (dictCode) => {
  return defHttp.get({ url: Api.getDictItems + dictCode }, { joinTime: false });
};
/**
 * 部门用户modal选择列表加载list
 */
export const getTableList = (params) => {
  return defHttp.get({ url: Api.getTableList, params });
};
/**
 * 加载全部分类字典数据
 */
export const loadCategoryData = (params) => {
  return defHttp.get({ url: Api.getCategoryData, params });
};
/**
 * 文件上传
 */
export const uploadFile = (params, success) => {
  return defHttp.uploadFile({ url: uploadUrl }, params, { success });
};
/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export const downloadFile = (url, fileName?, parameter?) => {
  return getFileblob(url, parameter).then((data) => {
    if (!data || data.size === 0) {
      message.warning('文件下载失败');
      return;
    }
    // @ts-ignore
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      // @ts-ignore
      window.navigator.msSaveBlob(new Blob([data]), fileName);
    } else {
      let url = window.URL.createObjectURL(new Blob([data]));
      let link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    }
  });
};

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export const getFileblob = (url, parameter) => {
  return defHttp.get(
    {
      url: url,
      params: parameter,
      responseType: 'blob',
    },
    { isTransformResponse: false }
  );
};

/**
 * 【用于评论功能】自定义文件上传-方法
 */
export const uploadMyFile = (url, data) => {
  return defHttp.uploadMyFile(url, data);
};

/**
 * 重复性校验
 * @param params
 */
export const duplicateCheck = (params) => {
  return defHttp.get({ url: Api.duplicateCheck, params });
};

/**
 * 刷新缓存
 */
export const refreshCache = () => {
  return defHttp.get({ url: Api.refreshCache });
};

/**
 * 查询所有字典项
 */
export const queryAllDictItems = () => {
  return defHttp.get({ url: Api.queryAllDictItems });
};

/**
 * 获取用户部门
 * @param params
 */
export const getUserDeparts = (params) => {
  return defHttp.get({ url: Api.getUserDeparts, params });
};

/**
 * 选择部门
 * @param params
 */
export const selectDepart = (params) => {
  return defHttp.post({ url: Api.selectDepart, params });
};

/**
 * 获取用户租户
 * @param params
 */
export const getUserTenants = (params) => {
  return defHttp.get({ url: Api.getUserTenants, params });
};
