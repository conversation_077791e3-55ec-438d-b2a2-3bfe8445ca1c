<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>电话</title>
    <defs>
        <path d="M309,845 L1896,845 C1898.20914,845 1900,846.790861 1900,849 L1900,1438 C1900,1440.20914 1898.20914,1442 1896,1442 L309,1442 C306.790861,1442 305,1440.20914 305,1438 L305,849 C305,846.790861 306.790861,845 309,845 Z" id="path-1"></path>
        <filter x="-1.1%" y="-2.7%" width="102.5%" height="106.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="2" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="6" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.12 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <rect id="path-3" x="0" y="0" width="24" height="24"></rect>
    </defs>
    <g id="B端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="管理页" transform="translate(-837.000000, -950.000000)">
            <rect fill="#F4F4F5" x="0" y="0" width="1920" height="1451"></rect>
            <g id="路径">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <path d="M773,912 L986.999939,912 C989.209078,912 990.999939,913.790861 990.999939,916 L990.999939,1133 C990.999939,1135.20914 989.209078,1137 986.999939,1137 L773,1137 C770.790861,1137 769,1135.20914 769,1133 L769,916 C769,913.790861 770.790861,912 773,912 Z" id="路径" stroke="#FFFFFF" stroke-width="2" fill="#EBF7FF" stroke-dasharray="0,0"></path>
            <g id="电话" transform="translate(837.000000, 950.000000)">
                <mask id="mask-4" fill="white">
                    <use xlink:href="#path-3"></use>
                </mask>
                <g id="路径"></g>
                <path d="M8.49789079,3.84277248 C8.86119083,3.84277248 9.19588086,4.03978251 9.37223088,4.35740256 L10.595431,6.56080787 C10.755581,6.84930791 10.763131,7.19825796 10.615531,7.493408 L9.43713089,9.85027334 C9.66479758,11.0207402 10.255031,12.0823737 11.2078311,13.0351738 C12.1606312,13.9879739 13.2203146,14.5762407 14.3868814,14.799974 L16.7433317,13.6217739 C17.0386817,13.4740739 17.3878818,13.4817239 17.6764818,13.6421739 L19.886132,14.8706741 C20.2034321,15.0471241 20.4002321,15.3816241 20.4002321,15.7446742 L20.4002321,18.2814245 C20.4002321,19.5732747 19.200282,20.5063249 17.9762318,20.0932748 C15.4622816,19.2450247 11.5599311,17.6299244 9.08653085,15.1564741 C6.61309058,12.6830737 4.9979954,8.78070819 4.14972781,6.26675783 C3.73671776,5.04270765 4.66976036,3.84277248 5.96159051,3.84277248 L8.49789079,3.84277248 L8.49789079,3.84277248 Z" id="路径" stroke="#50B5FF" stroke-width="2" stroke-linejoin="round" stroke-dasharray="0,0" fill-rule="nonzero" mask="url(#mask-4)"></path>
            </g>
        </g>
    </g>
</svg>