<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>警报</title>
    <defs>
        <path d="M309,845 L1896,845 C1898.20914,845 1900,846.790861 1900,849 L1900,1438 C1900,1440.20914 1898.20914,1442 1896,1442 L309,1442 C306.790861,1442 305,1440.20914 305,1438 L305,849 C305,846.790861 306.790861,845 309,845 Z" id="path-1"></path>
        <filter x="-1.1%" y="-2.7%" width="102.5%" height="106.7%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="2" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="6" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.12 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <rect id="path-3" x="0" y="0" width="24" height="24"></rect>
    </defs>
    <g id="B端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="管理页" transform="translate(-1466.000000, -950.000000)">
            <rect fill="#F4F4F5" x="0" y="0" width="1920" height="1451"></rect>
            <g id="路径">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#FFFFFF" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <path d="M1419.99997,912 L1633.99991,912 C1636.20905,912 1637.99991,913.790861 1637.99991,916 L1637.99991,1133 C1637.99991,1135.20914 1636.20905,1137 1633.99991,1137 L1419.99997,1137 C1417.79083,1137 1415.99997,1135.20914 1415.99997,1133 L1415.99997,916 C1415.99997,913.790861 1417.79083,912 1419.99997,912 Z" id="路径" stroke="#FFFFFF" stroke-width="2" fill="#EBF7FF" stroke-dasharray="0,0"></path>
            <g id="警报" transform="translate(1466.000000, 950.000000)">
                <mask id="mask-4" fill="white">
                    <use xlink:href="#path-3"></use>
                </mask>
                <g id="路径"></g>
                <path d="M7,12.5 C7,9.7386 9.2386,7.5 12,7.5 C14.7614,7.5 17,9.7386 17,12.5 L17,20.5 L7,20.5 L7,12.5 L7,12.5 Z" id="路径" stroke="#FF974A" stroke-width="2" stroke-linejoin="round" stroke-dasharray="0,0" fill-rule="nonzero" mask="url(#mask-4)"></path>
                <line x1="12" y1="2.5" x2="12" y2="4.02587891" id="路径" stroke="#FF974A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0,0" fill-rule="nonzero" mask="url(#mask-4)"></line>
                <line x1="17.945631" y1="4.66407901" x2="16.9814587" y2="5.81313103" id="路径" stroke="#FF974A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0,0" fill-rule="nonzero" mask="url(#mask-4)"></line>
                <line x1="21.1090775" y1="10.1435561" x2="19.6318436" y2="10.4040532" id="路径" stroke="#FF974A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0,0" fill-rule="nonzero" mask="url(#mask-4)"></line>
                <line x1="2.89061308" y1="10.1435528" x2="4.36784697" y2="10.4040075" id="路径" stroke="#FF974A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0,0" fill-rule="nonzero" mask="url(#mask-4)"></line>
                <line x1="6.05421305" y1="4.66407889" x2="7.01838541" y2="5.81313592" id="路径" stroke="#FF974A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0,0" fill-rule="nonzero" mask="url(#mask-4)"></line>
                <line x1="3" y1="20.4999996" x2="21.5" y2="20.4999996" id="路径" stroke="#FF974A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="0,0" fill-rule="nonzero" mask="url(#mask-4)"></line>
            </g>
        </g>
    </g>
</svg>