<template>
    <div :class="prefixCls" class="login-background-img">
        <div class="aui-logo" v-if="!getIsMobile">
            <div>
                <h3>
                    <img :src="logoImg" alt="jeecg" />
                </h3>
            </div>
        </div>
        <div v-else class="aui-phone-logo">
            <img :src="logoImg" alt="jeecg" />
        </div>
        <div>
            <div class="aui-content">
                <div class="aui-container flex">
                    <div class="logo_text">
                        <div style="font-size: 62px; font-weight: bolder; color: #000000">
                            广汽国际用户运营平台
                            <div style="font-size: 26px">GAC International User Operation Platform</div>
                        </div>
                    </div>
                    <div class="aui-form">
                        <div class="aui-formBox">
                            <div class="aui-formWell">
                                <div class="investment_title">
                                    <span>登录</span>
                                </div>
                                <div class="investment_sub_title pb-24px">
                                    <span>欢迎使用 广汽国际用户运营平台</span>
                                </div>
                                <div class="aui-form-box">
                                    <a-form ref="loginRef" :model="formData" @keyup.enter.native="accountLogin">
                                        <div class="aui-account">
                                            <div class="aui-inputClear">
                                                <a-form-item>
                                                    <a-input class="fix-auto-fill" placeholder="请输入账号ID" v-model:value="formData.username" />
                                                </a-form-item>
                                            </div>
                                            <div class="aui-inputClear">
                                                <a-form-item>
                                                    <a-input
                                                        class="fix-auto-fill"
                                                        type="password"
                                                        placeholder="请输入密码"
                                                        v-model:value="formData.password"
                                                    />
                                                </a-form-item>
                                            </div>
                                            <div class="aui-inputClear">
                                                <a-form-item>
                                                    <a-input
                                                        class="fix-auto-fill"
                                                        type="text"
                                                        placeholder="请输入验证码"
                                                        v-model:value="formData.inputCode"
                                                    />
                                                </a-form-item>
                                                <div class="aui-code">
                                                    <span
                                                        v-if="isShowGetCode"
                                                        style="color: #1990ff; font-size: 18px"
                                                        @click.stop="handleChangeCheckCode"
                                                        >获取验证码</span
                                                    >
                                                    <StatisticCountdown
                                                        v-else
                                                        :valueStyle="{ color: '#1990FF', fontSize: '18px' }"
                                                        :value="Date.now() + 1000 * 60"
                                                        :format="`ss${t('common.retrieveSsSecondsLater')}`"
                                                        @finish="isShowGetCode = false"
                                                    />
                                                </div>
                                            </div>
                                            <div class="flex other-infos">
                                                <div class="select-other-info">
                                                    <a-form-item style="width: 100%">
                                                        <a-select
                                                            class="select-every"
                                                            v-model:value="formData.loginSite"
                                                            :options="[
                                                                {
                                                                    label: '统括平台',
                                                                    value: 1
                                                                }
                                                            ]"
                                                        ></a-select>
                                                    </a-form-item>
                                                </div>
                                                <div class="select-other-info language-form">
                                                    <a-form-item style="width: 100%">
                                                        <a-select
                                                            class="select-every"
                                                            v-model:value="formData.language"
                                                            :options="newLanguageData"
                                                        ></a-select>
                                                    </a-form-item>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="aui-formButton">
                                            <a-button :loading="loginLoading" class="aui-link-login" type="primary" @click="accountLogin">登录</a-button>
                                        </div>
                                    </a-form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup name="login-mini">
import { reactive, ref, toRaw, onMounted } from 'vue'
import { StatisticCountdown } from 'ant-design-vue'
import { useUserStore } from '/@/store/modules/user'
import { useMessage } from '/@/hooks/web/useMessage'
import { useI18n } from '/@/hooks/web/useI18n'
import logoImg from '/@/assets/loginmini/icon/logo.png'
import { useDesign } from '/@/hooks/web/useDesign'
import { useAppInject } from '/@/hooks/web/useAppInject'
import { getLanguageTypeApi } from '/@/api/common/api'
import { sendLoginMsgApi } from '/@/api/common/api'

const { prefixCls } = useDesign('mini-login')
const { notification, createMessage } = useMessage()
const userStore = useUserStore()
const { t } = useI18n()

const newLanguageData = ref([
    {
        value: 'zh',
        label: '中文'
    },
    {
        value: 'en',
        label: 'English'
    },
    {
        value: 'es',
        label: 'Español'
    },
    {
        value: 'pt',
        label: 'Português'
    },
    {
        value: 'ar',
        label: 'اللغة العربية'
    }
])

// 是否展示重新获取验证码
const isShowGetCode = ref(true)

//账号登录表单字段
const formData = reactive<any>({
    inputCode: '',
    username: '17699999999',
    password: 'GAC*UOP!',
    loginSite: 1,
    language: 'zh'
})
const loginRef = ref()
const loginLoading = ref<boolean>(false)
const { getIsMobile } = useAppInject()
defineProps({
    sessionTimeout: {
        type: Boolean
    }
})

/**
 * 获取验证码
 */
async function handleChangeCheckCode() {
    if (!formData.username) {
        createMessage.warn('请输入用户ID')
        return
    }
    if (!formData.language) {
        createMessage.warn('请输入密码')
        return
    }
    await sendLoginMsgApi({
        account: formData.username,
        language: formData.language
    })
    createMessage.success('登录成功')
    isShowGetCode.value = false
}

/**
 * 账号或者手机登录
 */
async function accountLogin() {
    if (!formData.username) {
        createMessage.warn('请输入用户ID')
        return
    }
    if (!formData.password) {
        createMessage.warn('请输入密码')
        return
    }
    if (!formData.inputCode) {
        createMessage.warn('请输入验证码')
        return
    }
    try {
        loginLoading.value = true
        // @ts-ignore
        const { userInfo } = await userStore.login(
            toRaw({
                account: formData.username,
                password: formData.password,
                code: formData.inputCode
            })
        )
    } catch (error) {
    } finally {
        loginLoading.value = false
    }
}

onMounted(() => {
    // getAllLanguageFn();
})
</script>

<style lang="less" scoped>
@import '/@/assets/loginmini/style/home.less';
@import '/@/assets/loginmini/style/base.less';
::v-deep .ant-select-selection-item {
    display: flex;
    align-items: center;
}
@media (max-width: 1400px) {
    .aui-container {
        display: initial;
    }
    .aui-form {
        width: 100%;
    }
    .aui-container > .logo_text {
        display: flex;
        justify-content: center;
    }
}
@media (max-width: 500px) {
    .aui-container {
        display: initial;
    }
    .aui-form {
        width: 100%;
    }
    .aui-container > .logo_text {
        display: none;
    }
}
:deep(.ant-input:focus) {
    box-shadow: none;
}
.aui-get-code {
    float: right;
    position: relative;
    z-index: 3;
    background: #ffffff;
    color: #1573e9;
    border-radius: 100px;
    padding: 5px 16px;
    margin: 7px;
    border: 1px solid #1573e9;
    top: 12px;
}

.aui-get-code:hover {
    color: #1573e9;
}

.code-shape {
    border-color: #dadada !important;
    color: #aaa !important;
}

:deep(.jeecg-dark-switch) {
    position: absolute;
    margin-right: 10px;
}

.other-infos {
    justify-content: space-between;
}
.select-other-info {
    width: 49%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px;
    overflow: hidden;
    .ant-form-item {
        width: 100%;
    }
}
.aui-link-login {
    width: 100%;
    height: 65px;
    padding: 10px 15px;
    font-size: 18px;
    border-radius: 20px;
    margin-top: 40px;
    flex: 1;
    color: #fff;
}
.language-form {
    margin-left: 12px;
}
::v-deep(.ant-select-selector) {
    height: 65px !important;
    background-color: #f7f8fa !important;
    border: none !important;
}
.select-every {
    width: 100%;
}
.aui-phone-logo {
    position: absolute;
    margin-left: 10px;
    width: 60px;
    top: 2px;
    z-index: 4;
}
.top-3 {
    top: 0.45rem;
}
</style>

<style lang="less">
@prefix-cls: ~'@{namespace}-mini-login';
@dark-bg: #293146;

html[data-theme='dark'] {
    .@{prefix-cls} {
        background-color: @dark-bg !important;
        background-image: none;

        &::before {
            background-image: url(/@/assets/svg/login-bg-dark.svg);
        }
        .aui-inputClear {
            background-color: #232a3b !important;
        }
        .ant-input,
        .ant-input-password {
            background-color: #232a3b !important;
        }

        .ant-btn:not(.ant-btn-link):not(.ant-btn-primary) {
            border: 1px solid #4a5569 !important;
        }

        &-form {
            background: @dark-bg !important;
        }

        .app-iconify {
            color: #fff !important;
        }
        .aui-inputClear input,
        .aui-input-line input,
        .aui-choice {
            color: #c9d1d9 !important;
        }

        .aui-formBox {
            background-color: @dark-bg !important;
        }
        .aui-third-text span {
            background-color: @dark-bg !important;
        }
        .aui-form-nav .aui-flex-box {
            color: #c9d1d9 !important;
        }

        .aui-formButton .aui-linek-code {
            background: @dark-bg !important;
            color: white !important;
        }
        .aui-code-line {
            border-left: none !important;
        }
        .ant-checkbox-inner,
        .aui-success h3 {
            border-color: #c9d1d9;
        }
        //update-begin---author:wangshuai ---date:20230828  for：【QQYUN-6363】这个样式代码有问题，不在里面，导致表达式有问题------------
        &-sign-in-way {
            .anticon {
                font-size: 22px !important;
                color: #888 !important;
                cursor: pointer !important;

                &:hover {
                    color: @primary-color !important;
                }
            }
        }
        //update-end---author:wangshuai ---date:20230828  for：【QQYUN-6363】这个样式代码有问题，不在里面，导致表达式有问题------------
    }

    input.fix-auto-fill,
    .fix-auto-fill input {
        -webkit-text-fill-color: #c9d1d9 !important;
        box-shadow: inherit !important;
    }

    .ant-divider-inner-text {
        font-size: 12px !important;
        color: @text-color-secondary !important;
    }
    .aui-third-login a {
        background: transparent;
    }
}
</style>
